import { createClient } from '@supabase/supabase-js'
import { createBrowserClient, createServerClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const createClientComponentClient = () =>
  createBrowserClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client (to be used in server components)
export const createServerComponentClient = (cookieStore: { get: (name: string) => { value: string } | undefined }) =>
  createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value
      },
    },
  })

// Admin client with service role key
export const createAdminClient = () =>
  createClient(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY!, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  })

// Database types (will be generated from Supabase)
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          free_downloads_remaining: number
          is_seller: boolean
          seller_verified: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          free_downloads_remaining?: number
          is_seller?: boolean
          seller_verified?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          free_downloads_remaining?: number
          is_seller?: boolean
          seller_verified?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          slug: string
          icon: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          slug: string
          icon?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          slug?: string
          icon?: string | null
          created_at?: string
        }
      }
      templates: {
        Row: {
          id: string
          title: string
          description: string
          category_id: string
          price: number
          is_free: boolean
          file_url: string
          preview_images: string[] | null
          documentation: string
          video_url: string | null
          downloads_count: number
          rating: number
          rating_count: number
          seller_id: string
          is_active: boolean
          is_featured: boolean
          tags: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          category_id: string
          price?: number
          is_free?: boolean
          file_url: string
          preview_images?: string[] | null
          documentation: string
          video_url?: string | null
          downloads_count?: number
          rating?: number
          rating_count?: number
          seller_id: string
          is_active?: boolean
          is_featured?: boolean
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          category_id?: string
          price?: number
          is_free?: boolean
          file_url?: string
          preview_images?: string[] | null
          documentation?: string
          video_url?: string | null
          downloads_count?: number
          rating?: number
          rating_count?: number
          seller_id?: string
          is_active?: boolean
          is_featured?: boolean
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      purchases: {
        Row: {
          id: string
          user_id: string
          template_id: string
          amount: number
          stripe_payment_intent_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          template_id: string
          amount: number
          stripe_payment_intent_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          template_id?: string
          amount?: number
          stripe_payment_intent_id?: string
          created_at?: string
        }
      }
      downloads: {
        Row: {
          id: string
          user_id: string
          template_id: string
          is_free_download: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          template_id: string
          is_free_download?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          template_id?: string
          is_free_download?: boolean
          created_at?: string
        }
      }
    }
  }
}
