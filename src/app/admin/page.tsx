'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Header } from '@/components/layout/Header'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { createClientComponentClient } from '@/lib/supabase'
import { Database } from '@/lib/supabase'
import { toast } from 'sonner'
import { Plus, Edit, Trash2, Upload } from 'lucide-react'

type Template = Database['public']['Tables']['templates']['Row']
type Category = Database['public']['Tables']['categories']['Row']

export default function AdminPage() {
  const { user } = useAuth()
  const [templates, setTemplates] = useState<Template[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  
  const supabase = createClientComponentClient()

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category_id: '',
    price: 0,
    is_free: true,
    documentation: '',
    video_url: '',
    tags: '',
  })

  const fetchData = useCallback(async () => {
    try {
      // Fetch categories
      const { data: categoriesData } = await supabase
        .from('categories')
        .select('*')
        .order('name')

      // Fetch templates
      const { data: templatesData } = await supabase
        .from('templates')
        .select('*')
        .order('created_at', { ascending: false })

      setCategories(categoriesData || [])
      setTemplates(templatesData || [])
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }, [supabase])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    try {
      const templateData = {
        ...formData,
        seller_id: user.id,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        file_url: 'templates/placeholder.json', // This would be handled by file upload
      }

      const { error } = await supabase
        .from('templates')
        .insert([templateData])

      if (error) throw error

      toast.success('Template added successfully!')
      setShowAddForm(false)
      setFormData({
        title: '',
        description: '',
        category_id: '',
        price: 0,
        is_free: true,
        documentation: '',
        video_url: '',
        tags: '',
      })
      fetchData()
    } catch (error) {
      console.error('Error adding template:', error)
      toast.error('Failed to add template')
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <p>Please sign in to access the admin panel.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Template Management</h1>
            <p className="text-gray-600">Manage your automation templates</p>
          </div>
          <Button onClick={() => setShowAddForm(!showAddForm)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Template
          </Button>
        </div>

        {/* Add Template Form */}
        {showAddForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Add New Template</CardTitle>
              <CardDescription>
                Create a new automation template for the marketplace
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={formData.category_id}
                      onValueChange={(value) => setFormData({ ...formData, category_id: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="price">Price ($)</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                    />
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <input
                      type="checkbox"
                      id="is_free"
                      checked={formData.is_free}
                      onChange={(e) => setFormData({ ...formData, is_free: e.target.checked })}
                    />
                    <Label htmlFor="is_free">Free Template</Label>
                  </div>
                </div>

                <div>
                  <Label htmlFor="documentation">Documentation</Label>
                  <textarea
                    id="documentation"
                    className="w-full p-2 border rounded-md"
                    rows={4}
                    value={formData.documentation}
                    onChange={(e) => setFormData({ ...formData, documentation: e.target.value })}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="video_url">Video URL (optional)</Label>
                    <Input
                      id="video_url"
                      type="url"
                      value={formData.video_url}
                      onChange={(e) => setFormData({ ...formData, video_url: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="tags">Tags (comma-separated)</Label>
                    <Input
                      id="tags"
                      value={formData.tags}
                      onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                      placeholder="automation, n8n, workflow"
                    />
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button type="submit">Add Template</Button>
                  <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Templates List */}
        <div className="grid grid-cols-1 gap-6">
          {loading ? (
            <div className="text-center py-8">Loading templates...</div>
          ) : templates.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Upload className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                <p className="text-gray-500">No templates yet</p>
                <p className="text-sm text-gray-400">Add your first template to get started</p>
              </CardContent>
            </Card>
          ) : (
            templates.map((template) => (
              <Card key={template.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {template.title}
                        <Badge variant={template.is_free ? "secondary" : "default"}>
                          {template.is_free ? 'Free' : `$${template.price}`}
                        </Badge>
                        {template.is_featured && (
                          <Badge variant="outline">Featured</Badge>
                        )}
                      </CardTitle>
                      <CardDescription>{template.description}</CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>Downloads: {template.downloads_count}</span>
                    <span>Rating: {template.rating.toFixed(1)}/5</span>
                    <span>Created: {new Date(template.created_at).toLocaleDateString()}</span>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
