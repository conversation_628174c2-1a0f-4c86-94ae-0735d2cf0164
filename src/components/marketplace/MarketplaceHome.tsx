'use client'

import { Head<PERSON> } from '@/components/layout/Header'
import { HeroSection } from '@/components/agency/HeroSection'
import { ServicesSection } from '@/components/agency/ServicesSection'
import { SectionTransition } from '@/components/ui/section-transition'
import { Footer } from '@/components/ui/footer-section'
import { Stats } from '@/components/ui/stats-section-with-text'
import { ContactSection } from '@/components/agency/ContactSection'

export function AgencyHome() {

  return (
    <div className="min-h-screen bg-black dark relative overflow-hidden">
      {/* Optimized ambient background with strategic gradient overlays */}
      <div className="fixed inset-0 pointer-events-none performance-optimized">
        {/* Primary ambient gradient overlay */}
        <div className="absolute inset-0 ambient-gradient-overlay opacity-50 gradient-optimized" />

        {/* Optimized animated gradient orb for depth */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-yellow-400/3 via-orange-500/2 to-red-500/2 rounded-full blur-3xl animate-pulse performance-optimized" style={{ animationDuration: '10s' }} />
      </div>

      <Header />

      {/* New Hero Section */}
      <HeroSection />

      {/* Smooth Transition from Hero to Featured Templates */}
      <SectionTransition
        height="md"
        variant="dark-to-dark"
        className="relative z-10 -mt-8"
      />

      {/* Services Section */}
      <ServicesSection />

      {/* Smooth Transition from Services to Stats */}
      <SectionTransition
        height="md"
        variant="dark-to-dark"
        className="relative z-10"
      />

      {/* Stats Section */}
      <Stats />

      {/* Smooth Transition from Stats to Contact */}
      <SectionTransition
        height="lg"
        variant="dark-to-dark"
        className="relative z-10"
      />

      {/* Contact Section */}
      <ContactSection />

      {/* Footer */}
      <Footer />
    </div>
  )
}
