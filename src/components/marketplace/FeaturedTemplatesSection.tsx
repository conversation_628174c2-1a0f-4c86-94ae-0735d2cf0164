"use client";

import { useState, useEffect } from "react";
import { Bot, Workflow, Zap, Database, Mail, Calendar, FileText, Target, ShoppingCart, Users, BarChart3, Plug } from "lucide-react";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import { cn } from "@/lib/utils";
import { createClientComponentClient } from "@/lib/supabase";
import { Database as DatabaseType } from "@/lib/supabase";

type Template = DatabaseType['public']['Tables']['templates']['Row'] & {
  categories: DatabaseType['public']['Tables']['categories']['Row'] | null;
};

// Icon mapping for categories
const categoryIcons = {
  'chatbots': Bot,
  'workflow-automation': Workflow,
  'content-generation': FileText,
  'data-processing': Database,
  'email-marketing': Mail,
  'social-media': Zap,
  'lead-generation': Target,
  'ecommerce': ShoppingCart,
  'crm-integration': Users,
  'analytics-reporting': BarChart3,
  'api-integrations': Plug,
  'productivity-tools': Calendar,
};

export function FeaturedTemplatesSection() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient();

  useEffect(() => {
    async function fetchFeaturedTemplates() {
      try {
        setError(null);

        const { data, error } = await supabase
          .from('templates')
          .select(`
            *,
            categories (
              name,
              slug,
              icon
            )
          `)
          .eq('is_featured', true)
          .eq('is_active', true)
          .order('downloads_count', { ascending: false })
          .limit(6);

        if (error) {
          console.error('Error fetching featured templates:', error);

          // Check if it's a table not found error (database not set up)
          if (error.code === '42501' || error.code === '42P01') {
            setError('database_not_setup');
          } else {
            setError('fetch_error');
          }
          return;
        }

        setTemplates(data || []);
      } catch (error) {
        console.error('Unexpected error fetching templates:', error);
        setError('unexpected_error');
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedTemplates();
  }, [supabase]);

  const formatDownloads = (count: number): string => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  const getGridArea = (index: number): string => {
    const areas = [
      "md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]",
      "md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]",
      "md:[grid-area:2/1/3/7] xl:[grid-area:1/5/2/9]",
      "md:[grid-area:2/7/3/13] xl:[grid-area:1/9/2/13]",
      "md:[grid-area:3/1/4/7] xl:[grid-area:2/5/3/9]",
      "md:[grid-area:3/7/4/13] xl:[grid-area:2/9/3/13]",
    ];
    return areas[index] || "";
  };

  return (
    <section className="pt-8 md:pt-16 pb-16 md:pb-32 bg-black/[0.96] relative w-full overflow-hidden">
      {/* Enhanced ambient background for featured templates */}
      <div className="absolute inset-0 ambient-gradient-overlay-center opacity-60" />
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-yellow-400/5 via-orange-500/4 to-transparent rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-tl from-red-500/4 via-orange-500/3 to-transparent rounded-full blur-3xl" />

      <div className="container mx-auto space-y-12 relative z-10">
        {/* Section Header */}
        <div className="relative z-10 grid items-center gap-6 md:grid-cols-2 md:gap-12">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-semibold bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400">
            Featured Templates
          </h2>
          <p className="max-w-lg sm:ml-auto text-neutral-300 leading-relaxed text-lg md:text-xl">
            Discover our most popular AI automation templates, carefully crafted to streamline your workflows and boost productivity across various business functions.
          </p>
        </div>

        {/* Featured Templates Grid */}
        <div className="relative z-10">
          {loading ? (
            <div className="flex items-center justify-center py-16">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
              <p className="ml-4 text-neutral-400">Loading templates...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-16">
              <p className="text-red-400 mb-2">Error loading templates</p>
              <p className="text-neutral-500 text-sm">Please try refreshing the page</p>
            </div>
          ) : templates.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16">
              <p className="text-neutral-400 mb-2">No featured templates found.</p>
              <p className="text-neutral-500 text-sm">Check back soon for new templates!</p>
            </div>
          ) : (
            <ul className="grid grid-cols-1 grid-rows-none gap-4 md:grid-cols-12 md:grid-rows-3 lg:gap-6 xl:max-h-[36rem] xl:grid-rows-2">
              {templates.map((template, index) => {
                const IconComponent = categoryIcons[template.categories.slug as keyof typeof categoryIcons] || Bot;
                return (
                  <GridItem
                    key={template.id}
                    area={getGridArea(index)}
                    icon={<IconComponent className="h-5 w-5" />}
                    title={template.title}
                    description={template.description}
                    category={template.categories.name}
                    downloads={formatDownloads(template.downloads_count)}
                    price={template.price}
                    isFree={template.is_free}
                    rating={template.rating}
                    previewImage={template.preview_images?.[0]}
                  />
                );
              })}
            </ul>
          )}
        </div>
      </div>
    </section>
  );
}

interface GridItemProps {
  area: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  category: string;
  downloads: string;
  price: number;
  isFree: boolean;
  rating: number;
  previewImage?: string;
}

const GridItem = ({
  area,
  icon,
  title,
  description,
  category,
  downloads,
  price,
  isFree,
  rating,
  previewImage
}: GridItemProps) => {
  return (
    <li className={cn("min-h-[16rem] list-none", area)}>
      <div className="relative h-full rounded-[1.25rem] border-[0.75px] border-neutral-800/50 p-2 md:rounded-[1.5rem] md:p-3 hover:border-neutral-700/50 transition-all duration-300 group cursor-pointer">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
          borderWidth={2}
        />
        <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border-[0.75px] card-ambient-glow p-6 shadow-lg hover:shadow-2xl transition-all duration-300">
          {/* Subtle brand-colored ambient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/2 via-orange-500/3 to-red-500/2 rounded-xl opacity-60" />

          {/* Preview Image */}
          {previewImage && (
            <div className="absolute top-4 right-4 w-12 h-12 rounded-lg overflow-hidden border border-neutral-700/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <img
                src={previewImage}
                alt={`${title} preview`}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div className="relative flex flex-1 flex-col justify-between gap-4">
            {/* Icon and Category */}
            <div className="flex items-center justify-between">
              <div className="w-fit rounded-lg border-[0.75px] border-neutral-700/50 bg-gradient-to-br from-neutral-800/60 to-neutral-700/40 p-3">
                <div className="text-neutral-300">
                  {icon}
                </div>
              </div>
              <div className="flex flex-col items-end gap-1">
                <div className="text-xs text-neutral-500 font-medium">
                  {category}
                </div>
                {isFree ? (
                  <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full font-medium">
                    FREE
                  </span>
                ) : (
                  <span className="text-xs text-neutral-400 font-medium">
                    ${price}
                  </span>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="space-y-3 flex-1">
              <h3 className="pt-0.5 text-xl leading-[1.375rem] font-semibold font-sans tracking-[-0.04em] md:text-2xl md:leading-[1.875rem] text-balance text-neutral-50">
                {title}
              </h3>
              <p className="font-sans text-sm leading-[1.125rem] md:text-base md:leading-[1.375rem] text-neutral-400">
                {description}
              </p>
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between pt-2 border-t border-neutral-800/50">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-neutral-500">Downloads</span>
                  <span className="text-sm font-medium text-neutral-300">{downloads}</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-neutral-500">Rating</span>
                  <span className="text-sm font-medium text-yellow-400">{rating.toFixed(1)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </li>
  );
};
