'use client'

import { But<PERSON> } from "@/components/ui/button"
import { Mail, Calendar } from "lucide-react"
import Link from "next/link"

export function ReadyToAutomateSection() {
  return (
    <section className="py-20 md:py-28 lg:py-36 xl:py-44 bg-black/[0.96] relative w-full overflow-hidden">
      {/* Optimized ambient background lighting */}
      <div className="absolute inset-0 ambient-gradient-overlay opacity-50 performance-optimized" />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-yellow-400/3 via-orange-500/2 to-red-500/2 rounded-full blur-3xl animate-pulse performance-optimized" style={{ animationDuration: '8s' }} />

      <div className="container mx-auto relative z-10 max-w-6xl">
        {/* Full-width centered content */}
        <div className="flex flex-col items-center justify-center text-center">
          {/* Headline */}
          <div className="mb-8 lg:mb-12">
            <h2 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold leading-tight max-w-5xl mx-auto">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 block">
                Ready to Automate?
              </span>
            </h2>
          </div>

          {/* Description */}
          <div className="mb-10 lg:mb-14">
            <p className="text-lg md:text-xl lg:text-2xl xl:text-3xl text-neutral-300 max-w-4xl leading-relaxed mx-auto">
              Transform your business with AI automation today. Get personalized guidance
              from our experts or reach out directly to start your automation journey.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 lg:gap-8 justify-center items-center">
            <Button
              size="lg"
              className="text-white hover:text-white font-semibold text-lg md:text-xl lg:text-2xl px-8 md:px-12 lg:px-16 py-4 md:py-6 lg:py-8 transition-all duration-300 border-2 border-orange-500/70 hover:border-orange-500 backdrop-blur-sm bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-300 hover:via-orange-400 hover:to-red-400 shadow-lg hover:shadow-xl ring-1 ring-white/20 hover:ring-white/30 brand-glow-hover hover:scale-105 active:scale-95"
              asChild
            >
              <Link href="/book-call">
                <Calendar className="mr-3 md:mr-4 w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" />
                Book a Call
              </Link>
            </Button>
            <Button
              size="lg"
              variant="ghost"
              className="text-white hover:text-white font-semibold text-lg md:text-xl lg:text-2xl px-8 md:px-12 lg:px-16 py-4 md:py-6 lg:py-8 transition-all duration-300 border-2 border-white/30 hover:border-orange-500/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-orange-500/20 hover:to-red-500/30 shadow-sm hover:shadow-md brand-glow-hover hover:scale-105 active:scale-95"
              asChild
            >
              <Link href="mailto:<EMAIL>">
                <Mail className="mr-3 md:mr-4 w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" />
                Send Email
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
