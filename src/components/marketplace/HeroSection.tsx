'use client'

import { SplineScene } from "@/components/ui/splite";
import { Card } from "@/components/ui/card"
import { Spotlight } from "@/components/ui/spotlight"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Zap } from "lucide-react"
import Link from "next/link"

export function HeroSection() {
  return (
    <div className="pt-16 pb-8 md:pt-20 md:pb-12 lg:pt-24 lg:pb-16 relative">
      <Card className="w-full h-[650px] md:h-[700px] lg:h-[750px] bg-black/[0.96] relative overflow-hidden card-ambient-glow">
        {/* Optimized ambient lighting background */}
        <div className="absolute inset-0 hero-ambient-lighting opacity-60 performance-optimized" />

        {/* Optimized brand-colored gradients for depth */}
        <div className="absolute top-0 left-0 w-full h-full performance-optimized">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[300px] bg-gradient-to-r from-yellow-400/4 via-orange-500/3 to-red-500/2 rounded-full blur-3xl opacity-50" />
        </div>

        {/* Optimized spotlight with brand colors */}
        <Spotlight
          className="-top-40 left-0 md:left-60 md:-top-20 performance-optimized"
          fill="rgba(251, 191, 36, 0.6)"
        />

        <div className="flex h-full">
          {/* Left content - Aligned with navigation */}
          <div className="flex-1 relative z-10 flex flex-col justify-center">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-3">
                <div className="lg:col-span-2 ml-[4cm]">
            {/* Badge */}
            <div className="inline-flex items-center px-3 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium mb-6 w-fit">
              <Zap className="w-4 h-4 mr-2 text-yellow-400" />
              <span className="text-white">New: 100+ AI Templates Added</span>
            </div>

            {/* Main heading */}
            <div className="mb-6">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400 block mb-2">
                  AI Automation
                </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 block">
                  Made Simple
                </span>
              </h1>
            </div>

            {/* Description */}
            <div className="mb-8">
              <p className="text-base md:text-lg text-neutral-300 max-w-lg leading-relaxed">
                Discover thousands of ready-to-use AI automation templates.
                Transform your workflows with cutting-edge AI technology.
                Get 5 free downloads to start automating today.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 mb-6">
              <Button
                size="lg"
                className="text-white hover:text-white font-semibold transition-all duration-300 ease-out rounded-xl bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-300 hover:via-orange-400 hover:to-red-400 shadow-lg hover:shadow-xl hover:shadow-orange-500/25 border border-white/10 hover:border-white/20 hover:scale-[1.02] active:scale-[0.98]"
                asChild
              >
                <Link href="/templates">
                  Browse Templates
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </Button>
              <Button
                size="lg"
                variant="ghost"
                className="text-white hover:text-white font-medium transition-all duration-300 border border-white/30 hover:border-primary/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 shadow-sm hover:shadow-md"
                asChild
              >
                <Link href="/auth/register">
                  Start Free Trial
                </Link>
              </Button>
            </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right content - Optimized 3D Scene */}
          <div className="flex-1 relative performance-optimized">
            <SplineScene
              scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
              className="w-full h-full spline-scene-enhanced"
            />
          </div>
        </div>
      </Card>
    </div>
  )
}
