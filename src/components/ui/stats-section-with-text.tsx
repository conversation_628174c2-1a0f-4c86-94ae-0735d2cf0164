import { FileText, Download, CheckCircle, Award } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import { cn } from "@/lib/utils";

interface StatCardProps {
  icon: React.ReactNode;
  value: string;
  change: string;
  changeColor: string;
  label: string;
}

const StatCard = ({ icon, value, change, changeColor, label }: StatCardProps) => {
  return (
    <div className="min-h-[18rem] list-none">
      <div className="relative h-full rounded-[1.25rem] border-[0.75px] border-neutral-800/50 p-2 md:rounded-[1.5rem] md:p-3 hover:border-neutral-700/50 transition-all duration-300">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
          borderWidth={2}
        />
        <div className="relative flex h-full flex-col justify-between gap-8 overflow-hidden rounded-xl border-[0.75px] card-ambient-glow p-8 shadow-lg hover:shadow-2xl transition-all duration-300">
          {/* Subtle brand-colored ambient overlay for stats */}
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/3 via-orange-500/2 to-red-500/2 rounded-xl opacity-50" />
          <div className="relative flex flex-1 flex-col justify-between gap-6">
            {/* Icon */}
            <div className="flex items-start justify-between">
              <div className="w-fit rounded-xl border-[0.75px] border-neutral-700/50 bg-gradient-to-br from-neutral-800/60 to-neutral-700/40 p-4">
                <div className="text-neutral-300">
                  {icon}
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="space-y-6 flex-1 flex flex-col justify-end">
              <div className="space-y-3">
                <h3 className="font-mono text-4xl md:text-5xl lg:text-6xl tracking-[-0.02em] max-w-xl text-left font-bold flex flex-row gap-3 items-baseline text-neutral-50 leading-none">
                  {value}
                  <span className={cn("font-sans text-sm md:text-base tracking-normal font-semibold uppercase", changeColor)}>
                    {change}
                  </span>
                </h3>
                <p className="font-sans text-base md:text-lg leading-relaxed tracking-[-0.01em] text-neutral-400 max-w-xl text-left font-medium">
                  {label}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

function Stats() {
  return (
    <section className="pt-12 md:pt-20 lg:pt-24 pb-20 md:pb-32 lg:pb-40 bg-black/[0.96] relative w-full overflow-hidden">
      {/* Enhanced ambient lighting for stats section */}
      <div className="absolute inset-0 stats-ambient-lighting opacity-70" />
      <div className="absolute top-1/4 right-1/4 w-72 h-72 bg-gradient-to-bl from-yellow-400/4 via-orange-500/3 to-transparent rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-gradient-to-tr from-red-500/3 via-orange-500/2 to-transparent rounded-full blur-3xl" />

      <div className="container mx-auto space-y-16 md:space-y-20 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          <div className="flex gap-6 flex-col items-start">
            <div>
              <Badge className="bg-gradient-to-r from-yellow-400/20 to-orange-500/20 border border-orange-500/30 text-orange-400 hover:bg-orange-500/30 font-sans text-sm font-semibold px-4 py-2 tracking-wide uppercase">
                Analytics
              </Badge>
            </div>
            <div className="flex gap-6 flex-col">
              <h2 className="font-sans text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400 tracking-[-0.02em] lg:max-w-2xl text-left leading-[0.9] antialiased">
                Powering AI Automation at Scale
              </h2>
              <p className="font-sans text-xl md:text-2xl lg:max-w-2xl leading-relaxed tracking-[-0.01em] text-neutral-300 text-left font-medium antialiased">
                Our marketplace has become the go-to platform for businesses seeking
                powerful AI automation solutions. Join thousands of companies already
                transforming their workflows with our cutting-edge templates.
              </p>
            </div>
          </div>
          <div className="flex justify-center items-center">
            <div className="grid text-left grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 w-full gap-6 lg:gap-8">
              <StatCard
                icon={<FileText className="h-6 w-6" />}
                value="15K+"
                change="+24.3%"
                changeColor="text-emerald-400"
                label="Active Templates"
              />
              <StatCard
                icon={<Download className="h-6 w-6" />}
                value="2.4M+"
                change="+18.7%"
                changeColor="text-cyan-400"
                label="Total Downloads"
              />
              <StatCard
                icon={<CheckCircle className="h-6 w-6" />}
                value="98.7%"
                change="+2.1%"
                changeColor="text-violet-400"
                label="Success Rate"
              />
              <StatCard
                icon={<Award className="h-6 w-6" />}
                value="4.9"
                change="+0.2"
                changeColor="text-amber-400"
                label="Average Rating"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export { Stats };
