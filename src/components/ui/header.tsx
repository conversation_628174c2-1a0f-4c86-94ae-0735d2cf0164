"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Menu, MoveRight, X, ShoppingCart, User, LogOut, Settings } from "lucide-react";
import { useState } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";

function Header1() {
    const { user, profile, signOut } = useAuth();

    const navigationItems = [
        {
            title: "Home",
            href: "/",
            description: "",
        },
        {
            title: "Templates",
            description: "Discover AI automation templates for your business needs.",
            items: [
                {
                    title: "Browse All",
                    href: "/templates",
                },
                {
                    title: "Categories",
                    href: "/categories",
                },
                {
                    title: "Popular",
                    href: "/templates?sort=popular",
                },
                {
                    title: "New Releases",
                    href: "/templates?sort=newest",
                },
            ],
        },
        {
            title: "Marketplace",
            description: "Everything you need to know about our AI automation marketplace.",
            items: [
                {
                    title: "How it Works",
                    href: "/how-it-works",
                },
                {
                    title: "Pricing",
                    href: "/pricing",
                },
                {
                    title: "Become a Seller",
                    href: "/become-seller",
                },
                {
                    title: "Support",
                    href: "/support",
                },
            ],
        },
    ];

    const [isOpen, setOpen] = useState(false);
    return (
        <header className="w-full z-40 fixed top-0 left-0 nav-ambient-blur shadow-lg">
            {/* Enhanced ambient background for navigation */}
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/2 via-orange-500/3 to-red-500/2 opacity-50" />
            <div className="absolute inset-0 bg-gradient-to-b from-black/90 via-black/95 to-black/90" />

            <div className="container relative mx-auto min-h-20 flex gap-4 flex-row lg:grid lg:grid-cols-3 items-center">
                <div className="justify-start items-center gap-4 lg:flex hidden flex-row">
                    <NavigationMenu className="flex justify-start items-start">
                        <NavigationMenuList className="flex justify-start gap-4 flex-row">
                            {navigationItems.map((item) => (
                                <NavigationMenuItem key={item.title}>
                                    {item.href ? (
                                        <>
                                            <NavigationMenuLink>
                                                <Button
                                                    variant="ghost"
                                                    className="text-white hover:text-white font-medium transition-all duration-300 border border-white/20 hover:border-primary/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 shadow-sm hover:shadow-md brand-glow-hover border-glow-hover"
                                                >
                                                    {item.title}
                                                </Button>
                                            </NavigationMenuLink>
                                        </>
                                    ) : (
                                        <>
                                            <NavigationMenuTrigger className="font-medium text-sm text-white hover:text-white data-[state=open]:text-white transition-all duration-300 border border-white/20 hover:border-primary/60 data-[state=open]:border-primary/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 data-[state=open]:from-primary/20 data-[state=open]:to-primary/30 shadow-sm hover:shadow-md data-[state=open]:shadow-md">
                                                {item.title}
                                            </NavigationMenuTrigger>
                                            <NavigationMenuContent className="!w-[450px] p-4 bg-black/90 backdrop-blur-md border border-white/20 shadow-2xl">
                                                <div className="flex flex-col lg:grid grid-cols-2 gap-4">
                                                    <div className="flex flex-col h-full justify-between">
                                                        <div className="flex flex-col">
                                                            <p className="text-base text-white font-medium">{item.title}</p>
                                                            <p className="text-gray-300 text-sm">
                                                                {item.description}
                                                            </p>
                                                        </div>
                                                        <Button
                                                            size="sm"
                                                            className="mt-10 bg-primary text-primary-foreground hover:bg-primary/90 shadow-md"
                                                            asChild
                                                        >
                                                            <Link href="/templates">
                                                                Explore Templates
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                    <div className="flex flex-col text-sm h-full justify-end">
                                                        {item.items?.map((subItem) => (
                                                            <NavigationMenuLink
                                                                href={subItem.href}
                                                                key={subItem.title}
                                                                className="flex flex-row justify-between items-center hover:bg-gradient-to-r hover:from-primary/15 hover:to-primary/25 hover:text-white py-2 px-4 rounded transition-all duration-300 text-white border border-transparent hover:border-primary/30"
                                                            >
                                                                <span className="text-white">{subItem.title}</span>
                                                                <MoveRight className="w-4 h-4 text-gray-300" />
                                                            </NavigationMenuLink>
                                                        ))}
                                                    </div>
                                                </div>
                                            </NavigationMenuContent>
                                        </>
                                    )}
                                </NavigationMenuItem>
                            ))}
                        </NavigationMenuList>
                    </NavigationMenu>
                </div>
                <div className="flex lg:justify-center">
                    <Link href="/" className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-lg flex items-center justify-center shadow-lg">
                            <span className="text-white font-bold text-sm">AI</span>
                        </div>
                        <span className="font-bold text-xl text-white drop-shadow-lg">AutoMarket</span>
                    </Link>
                </div>
                <div className="flex justify-end w-full gap-4">
                    {user ? (
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-white hover:text-white transition-all duration-300 border border-white/20 hover:border-primary/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 shadow-sm hover:shadow-md"
                            >
                                <ShoppingCart className="w-4 h-4" />
                            </Button>

                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        className="relative h-8 w-8 rounded-full ring-2 ring-white/20 hover:ring-primary/60 transition-all duration-300 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 shadow-sm hover:shadow-md"
                                    >
                                        <Avatar className="h-8 w-8">
                                            <AvatarImage src={profile?.avatar_url || ''} alt={profile?.full_name || ''} />
                                            <AvatarFallback>
                                                {profile?.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}
                                            </AvatarFallback>
                                        </Avatar>
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="w-56 bg-black/90 backdrop-blur-md border border-white/20 shadow-2xl" align="end" forceMount>
                                    <div className="flex items-center justify-start gap-2 p-2">
                                        <div className="flex flex-col space-y-1 leading-none">
                                            <p className="font-medium text-white">{profile?.full_name || 'User'}</p>
                                            <p className="w-[200px] truncate text-sm text-gray-300">
                                                {user.email}
                                            </p>
                                        </div>
                                    </div>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem asChild>
                                        <Link href="/dashboard" className="flex items-center transition-all duration-300 text-white hover:text-white bg-gradient-to-r from-transparent to-transparent hover:from-primary/15 hover:to-primary/25 border border-transparent hover:border-primary/30 rounded-md">
                                            <User className="mr-2 h-4 w-4" />
                                            Dashboard
                                        </Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild>
                                        <Link href="/dashboard/settings" className="flex items-center transition-all duration-300 text-white hover:text-white bg-gradient-to-r from-transparent to-transparent hover:from-primary/15 hover:to-primary/25 border border-transparent hover:border-primary/30 rounded-md">
                                            <Settings className="mr-2 h-4 w-4" />
                                            Settings
                                        </Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator className="bg-white/20" />
                                    <DropdownMenuItem
                                        onClick={() => signOut()}
                                        className="transition-all duration-300 cursor-pointer text-white hover:text-red-300 bg-gradient-to-r from-transparent to-transparent hover:from-red-500/15 hover:to-red-500/25 border border-transparent hover:border-red-400/40 rounded-md"
                                    >
                                        <LogOut className="mr-2 h-4 w-4" />
                                        Sign out
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    ) : (
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="ghost"
                                className="text-white hover:text-white font-medium transition-all duration-300 border border-white/30 hover:border-primary/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 shadow-sm hover:shadow-md brand-glow-hover"
                                asChild
                            >
                                <Link href="/auth/login">Sign In</Link>
                            </Button>
                            <Button
                                className="text-white hover:text-white font-medium transition-all duration-300 border border-primary/50 hover:border-primary/70 backdrop-blur-sm bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-300 hover:via-orange-400 hover:to-red-400 shadow-lg hover:shadow-xl brand-glow-hover"
                                asChild
                            >
                                <Link href="/auth/register">Get Started</Link>
                            </Button>
                        </div>
                    )}
                </div>
                <div className="flex w-12 shrink lg:hidden items-end justify-end">
                    <Button
                        variant="ghost"
                        className="text-white hover:text-white transition-all duration-300 p-2 border border-white/20 hover:border-primary/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 shadow-sm hover:shadow-md"
                        onClick={() => setOpen(!isOpen)}
                    >
                        {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
                    </Button>
                    {isOpen && (
                        <div className="absolute top-20 border-t border-white/20 flex flex-col w-full right-0 bg-black/95 backdrop-blur-md shadow-2xl py-4 container gap-8">

                            {navigationItems.map((item) => (
                                <div key={item.title}>
                                    <div className="flex flex-col gap-2">
                                        {item.href ? (
                                            <Link
                                                href={item.href}
                                                className="flex justify-between items-center py-2 px-3 rounded-md transition-all duration-300 border border-transparent hover:border-primary/30 bg-gradient-to-r from-transparent to-transparent hover:from-primary/15 hover:to-primary/25"
                                                onClick={() => setOpen(false)}
                                            >
                                                <span className="text-lg font-medium text-white">{item.title}</span>
                                                <MoveRight className="w-4 h-4 stroke-1 text-white" />
                                            </Link>
                                        ) : (
                                            <p className="text-lg font-medium text-white">{item.title}</p>
                                        )}
                                        {item.items &&
                                            item.items.map((subItem) => (
                                                <Link
                                                    key={subItem.title}
                                                    href={subItem.href}
                                                    className="flex justify-between items-center py-2 px-3 rounded-md transition-all duration-300 ml-4 border border-transparent hover:border-primary/30 bg-gradient-to-r from-transparent to-transparent hover:from-primary/10 hover:to-primary/20"
                                                    onClick={() => setOpen(false)}
                                                >
                                                    <span className="text-gray-300 hover:text-white">
                                                        {subItem.title}
                                                    </span>
                                                    <MoveRight className="w-4 h-4 stroke-1 text-gray-300 hover:text-white" />
                                                </Link>
                                            ))}
                                    </div>
                                </div>
                            ))}

                            {/* Mobile Auth Actions */}
                            <div className="border-t border-white/20 pt-4">
                                {user ? (
                                    <div className="flex flex-col gap-2">
                                        <Link
                                            href="/dashboard"
                                            className="flex items-center gap-2 py-2 px-3 rounded-md transition-all duration-300 text-white border border-transparent hover:border-primary/30 bg-gradient-to-r from-transparent to-transparent hover:from-primary/15 hover:to-primary/25"
                                            onClick={() => setOpen(false)}
                                        >
                                            <User className="w-4 h-4" />
                                            Dashboard
                                        </Link>
                                        <Link
                                            href="/dashboard/settings"
                                            className="flex items-center gap-2 py-2 px-3 rounded-md transition-all duration-300 text-white border border-transparent hover:border-primary/30 bg-gradient-to-r from-transparent to-transparent hover:from-primary/15 hover:to-primary/25"
                                            onClick={() => setOpen(false)}
                                        >
                                            <Settings className="w-4 h-4" />
                                            Settings
                                        </Link>
                                        <button
                                            onClick={() => { signOut(); setOpen(false); }}
                                            className="flex items-center gap-2 py-2 px-3 rounded-md transition-all duration-300 text-left text-white border border-transparent hover:border-red-400/40 bg-gradient-to-r from-transparent to-transparent hover:from-red-500/15 hover:to-red-500/25 hover:text-red-300"
                                        >
                                            <LogOut className="w-4 h-4" />
                                            Sign out
                                        </button>
                                    </div>
                                ) : (
                                    <div className="flex flex-col gap-2">
                                        <Link href="/auth/login" onClick={() => setOpen(false)}>
                                            <Button
                                                variant="ghost"
                                                className="w-full justify-start text-white hover:text-white font-medium transition-all duration-300 border border-white/30 hover:border-primary/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 shadow-sm hover:shadow-md"
                                            >
                                                Sign In
                                            </Button>
                                        </Link>
                                        <Link href="/auth/register" onClick={() => setOpen(false)}>
                                            <Button className="w-full text-white hover:text-white font-medium transition-all duration-300 border border-primary/50 hover:border-primary/70 backdrop-blur-sm bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-300 hover:via-orange-400 hover:to-red-400 shadow-lg hover:shadow-xl">
                                                Get Started
                                            </Button>
                                        </Link>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </header>
    );
}

export { Header1 };