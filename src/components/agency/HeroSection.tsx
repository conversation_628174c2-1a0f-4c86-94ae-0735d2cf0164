'use client'

import { SplineScene } from "@/components/ui/splite";
import { Card } from "@/components/ui/card"
import { Spotlight } from "@/components/ui/spotlight"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Zap } from "lucide-react"
import Link from "next/link"

export function HeroSection() {
  return (
    <div className="pt-16 pb-8 md:pt-20 md:pb-12 lg:pt-24 lg:pb-16 relative">
      <Card className="w-full h-[700px] md:h-[750px] lg:h-[800px] xl:h-[850px] bg-black/[0.96] relative overflow-hidden card-ambient-glow">
        {/* Optimized ambient lighting background */}
        <div className="absolute inset-0 hero-ambient-lighting opacity-60 performance-optimized" />

        {/* Optimized brand-colored gradients for depth */}
        <div className="absolute top-0 left-0 w-full h-full performance-optimized">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[300px] bg-gradient-to-r from-yellow-400/4 via-orange-500/3 to-red-500/2 rounded-full blur-3xl opacity-50" />
        </div>

        {/* Optimized spotlight with brand colors */}
        <Spotlight
          className="-top-40 left-0 md:left-60 md:-top-20 performance-optimized"
          fill="rgba(251, 191, 36, 0.6)"
        />

        <div className="flex h-full">
          {/* Left content - Aligned with navigation */}
          <div className="flex-1 relative z-10 flex flex-col justify-center py-8 lg:py-12">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-3">
                <div className="lg:col-span-2 ml-[4cm] space-y-6 lg:space-y-8">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-3 bg-white/10 backdrop-blur-sm rounded-full text-base lg:text-lg font-medium mb-8 lg:mb-10 w-fit">
              <Zap className="w-5 h-5 lg:w-6 lg:h-6 mr-3 text-yellow-400" />
              <span className="text-white">Trusted by 500+ Businesses</span>
            </div>

            {/* Main heading */}
            <div className="mb-8 lg:mb-10">
              <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold leading-tight tracking-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400 block mb-3 lg:mb-4">
                  AI Automation
                </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 block">
                  Agency
                </span>
              </h1>
            </div>

            {/* Description */}
            <div className="mb-10 lg:mb-12">
              <p className="text-lg md:text-xl lg:text-2xl xl:text-3xl text-neutral-300 max-w-2xl lg:max-w-3xl leading-relaxed font-medium">
                Transform your business with custom AI automation solutions.
                We design, build, and deploy intelligent workflows that save time,
                reduce costs, and boost productivity.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 lg:gap-6 mb-6">
              <Button
                size="lg"
                className="text-white hover:text-white font-semibold text-lg lg:text-xl px-8 lg:px-10 py-4 lg:py-5 transition-all duration-300 ease-out rounded-xl bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-300 hover:via-orange-400 hover:to-red-400 shadow-lg hover:shadow-xl hover:shadow-orange-500/25 border border-white/10 hover:border-white/20 hover:scale-[1.02] active:scale-[0.98]"
                asChild
              >
                <Link href="#contact">
                  Get Free Consultation
                  <ArrowRight className="ml-3 w-5 h-5 lg:w-6 lg:h-6" />
                </Link>
              </Button>
              <Button
                size="lg"
                variant="ghost"
                className="text-white hover:text-white font-medium text-lg lg:text-xl px-8 lg:px-10 py-4 lg:py-5 transition-all duration-300 border border-white/30 hover:border-primary/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-primary/20 hover:to-primary/30 shadow-sm hover:shadow-md"
                asChild
              >
                <Link href="#services">
                  View Our Services
                </Link>
              </Button>
            </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right content - Optimized 3D Scene */}
          <div className="flex-1 relative performance-optimized">
            <SplineScene
              scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
              className="w-full h-full spline-scene-enhanced"
            />
          </div>
        </div>
      </Card>
    </div>
  )
}
